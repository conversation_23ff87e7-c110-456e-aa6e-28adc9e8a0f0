Starting gRPC performance test [3lercb] with 100 batches of 100 signals each
Target: localhost:9090
[3lercb] Batch 1/100: 100 signals, 117.8355ms, success: true
[3lercb] Batch 2/100: 100 signals, 62.419ms, success: true
[3lercb] Batch 3/100: 100 signals, 396.812708ms, success: true
[3lercb] Batch 4/100: 100 signals, 81.807ms, success: true
[3lercb] Batch 5/100: 100 signals, 118.071625ms, success: true
[3lercb] Batch 6/100: 100 signals, 75.032667ms, success: true
[3lercb] Batch 7/100: 100 signals, 58.452042ms, success: true
[3lercb] Batch 8/100: 100 signals, 57.8315ms, success: true
[3lercb] Batch 9/100: 100 signals, 59.35825ms, success: true
[3lercb] Batch 10/100: 100 signals, 56.197833ms, success: true
[3lercb] Batch 50/100: 100 signals, 58.752459ms, success: true
[3lercb] Batch 100/100: 100 signals, 50.02475ms, success: true

============================================================
PERFORMANCE TEST RESULTS [3lercb]
============================================================
Total Requests:      100
Successful Batches:  100
Failed Batches:      0
Total Signals:       10000
============================================================
TIMING METRICS:
Total Test Duration: 6.163142s
Total Request Time:  6.162162211s
Average Latency:     61.621622ms
Min Latency:         43.077625ms
Max Latency:         424.54175ms
Latency Range:       381.464125ms (9.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     16.23
Signals/Second:      1622.55
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
