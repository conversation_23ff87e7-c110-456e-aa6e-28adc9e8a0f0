Starting performance test [1nit9x] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 1/10: 50 signals, 204.504875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 2/10: 50 signals, 13.74125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 3/10: 50 signals, 23.937667ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 4/10: 50 signals, 12.445625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 5/10: 50 signals, 4.646375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 6/10: 50 signals, 3.946791ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 7/10: 50 signals, 5.975ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 8/10: 50 signals, 8.456958ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 9/10: 50 signals, 4.615667ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1nit9x] Batch 10/10: 50 signals, 11.752167ms, success: false

============================================================
PERFORMANCE TEST RESULTS [1nit9x]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 294.088708ms
Total Request Time:  294.022375ms
Average Latency:     0s
Min Latency:         3.946791ms
Max Latency:         204.504875ms
Latency Range:       200.558084ms (51.8x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     34.00
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
