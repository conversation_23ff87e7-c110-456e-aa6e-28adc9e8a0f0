Starting performance test [1hjydn] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[1hjydn] Batch 1/10: 50 signals, 319.147083ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 2/10: 50 signals, 3.705417ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 3/10: 50 signals, 1.559125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 4/10: 50 signals, 4.1035ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 5/10: 50 signals, 2.8525ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 6/10: 50 signals, 2.448542ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 7/10: 50 signals, 3.39375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 8/10: 50 signals, 2.971958ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 9/10: 50 signals, 1.910958ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1hjydn] Batch 10/10: 50 signals, 1.874209ms, success: false

============================================================
PERFORMANCE TEST RESULTS [1hjydn]
============================================================
Total Requests:      10
Successful Batches:  1
Failed Batches:      9
Total Signals:       50
============================================================
TIMING METRICS:
Total Test Duration: 344.037959ms
Total Request Time:  343.967042ms
Average Latency:     343.967042ms
Min Latency:         1.559125ms
Max Latency:         319.147083ms
Latency Range:       317.587958ms (204.7x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     29.07
Signals/Second:      145.33
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 9 batches failed
