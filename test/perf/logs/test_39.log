Starting performance test [1h4qro] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 1/10: 50 signals, 176.729541ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 2/10: 50 signals, 8.735708ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 3/10: 50 signals, 12.594625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 4/10: 50 signals, 16.772791ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 5/10: 50 signals, 30.42175ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 6/10: 50 signals, 8.0305ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 7/10: 50 signals, 3.104083ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 8/10: 50 signals, 13.010917ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 9/10: 50 signals, 7.201791ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1h4qro] Batch 10/10: 50 signals, 3.230417ms, success: false

============================================================
PERFORMANCE TEST RESULTS [1h4qro]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 279.918709ms
Total Request Time:  279.832123ms
Average Latency:     0s
Min Latency:         3.104083ms
Max Latency:         176.729541ms
Latency Range:       173.625458ms (56.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     35.72
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
