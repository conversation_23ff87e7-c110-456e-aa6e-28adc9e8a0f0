Starting performance test [gjtqi0] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[gjtqi0] Batch 1/10: 50 signals, 709.310041ms, success: true
[gjtqi0] Batch 2/10: 50 signals, 709.958625ms, success: true
[gjtqi0] Batch 3/10: 50 signals, 774.955ms, success: true
[gjtqi0] Batch 4/10: 50 signals, 1.107934042s, success: true
[gjtqi0] Batch 5/10: 50 signals, 1.124949s, success: true
[gjtqi0] Batch 6/10: 50 signals, 1.659112s, success: true
[gjtqi0] Batch 7/10: 50 signals, 1.5016785s, success: true
[gjtqi0] Batch 8/10: 50 signals, 1.529330625s, success: true
[gjtqi0] Batch 9/10: 50 signals, 1.403927083s, success: true
[gjtqi0] Batch 10/10: 50 signals, 1.394627791s, success: true

============================================================
PERFORMANCE TEST RESULTS [gjtqi0]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.916345709s
Total Request Time:  11.915782707s
Average Latency:     1.19157827s
Min Latency:         709.310041ms
Max Latency:         1.659112s
Latency Range:       949.801959ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.84
Signals/Second:      41.96
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
