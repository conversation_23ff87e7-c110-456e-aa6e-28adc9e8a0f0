Starting performance test [deulot] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[deulot] Batch 1/10: 50 signals, 201.752042ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 2/10: 50 signals, 20.849084ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 3/10: 50 signals, 27.921459ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 4/10: 50 signals, 11.601042ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 5/10: 50 signals, 12.56825ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 6/10: 50 signals, 7.38475ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 7/10: 50 signals, 7.922875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 8/10: 50 signals, 5.92775ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 9/10: 50 signals, 16.39125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[deulot] Batch 10/10: 50 signals, 5.953542ms, success: false

============================================================
PERFORMANCE TEST RESULTS [deulot]
============================================================
Total Requests:      10
Successful Batches:  1
Failed Batches:      9
Total Signals:       50
============================================================
TIMING METRICS:
Total Test Duration: 318.49425ms
Total Request Time:  318.272044ms
Average Latency:     318.272044ms
Min Latency:         5.92775ms
Max Latency:         201.752042ms
Latency Range:       195.824292ms (34.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     31.40
Signals/Second:      156.99
Overhead Ratio:      0.1% (non-request time)
============================================================
❌ 9 batches failed
