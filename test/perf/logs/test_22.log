Starting performance test [zv4ce3] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 1/10: 50 signals, 224.147083ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 2/10: 50 signals, 17.286875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 3/10: 50 signals, 12.818625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 4/10: 50 signals, 5.506291ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 5/10: 50 signals, 6.862333ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 6/10: 50 signals, 13.439417ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 7/10: 50 signals, 4.985042ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 8/10: 50 signals, 7.961416ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 9/10: 50 signals, 14.081416ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[zv4ce3] Batch 10/10: 50 signals, 5.6475ms, success: false

============================================================
PERFORMANCE TEST RESULTS [zv4ce3]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 312.874916ms
Total Request Time:  312.735998ms
Average Latency:     0s
Min Latency:         4.985042ms
Max Latency:         224.147083ms
Latency Range:       219.162041ms (45.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     31.96
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
