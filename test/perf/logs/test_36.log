Starting performance test [8fa9p3] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[8fa9p3] Batch 1/10: 50 signals, 731.248583ms, success: true
[8fa9p3] Batch 2/10: 50 signals, 701.8985ms, success: true
[8fa9p3] Batch 3/10: 50 signals, 866.677125ms, success: true
[8fa9p3] Batch 4/10: 50 signals, 995.430917ms, success: true
[8fa9p3] Batch 5/10: 50 signals, 1.1335635s, success: true
[8fa9p3] Batch 6/10: 50 signals, 1.657723292s, success: true
[8fa9p3] Batch 7/10: 50 signals, 1.506002958s, success: true
[8fa9p3] Batch 8/10: 50 signals, 1.71669125s, success: true
[8fa9p3] Batch 9/10: 50 signals, 1.557957959s, success: true
[8fa9p3] Batch 10/10: 50 signals, 1.333980584s, success: true

============================================================
PERFORMANCE TEST RESULTS [8fa9p3]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.202047834s
Total Request Time:  12.201174668s
Average Latency:     1.220117466s
Min Latency:         701.8985ms
Max Latency:         1.71669125s
Latency Range:       1.01479275s (2.4x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.82
Signals/Second:      40.98
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
