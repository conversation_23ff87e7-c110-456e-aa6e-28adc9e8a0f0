Starting performance test [4x6063] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[4x6063] Batch 1/10: 50 signals, 333.528958ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[4x6063] Batch 2/10: 50 signals, 2.668875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[4x6063] Batch 3/10: 50 signals, 2.884708ms, success: false
[4x6063] Batch 4/10: 50 signals, 791.66925ms, success: true
[4x6063] Batch 5/10: 50 signals, 761.512208ms, success: true
[4x6063] Batch 6/10: 50 signals, 1.098467208s, success: true
[4x6063] Batch 7/10: 50 signals, 1.017206625s, success: true
[4x6063] Batch 8/10: 50 signals, 1.4113705s, success: true
[4x6063] Batch 9/10: 50 signals, 1.499795292s, success: true
[4x6063] Batch 10/10: 50 signals, 1.696373042s, success: true

============================================================
PERFORMANCE TEST RESULTS [4x6063]
============================================================
Total Requests:      10
Successful Batches:  8
Failed Batches:      2
Total Signals:       400
============================================================
TIMING METRICS:
Total Test Duration: 8.615813458s
Total Request Time:  8.615476666s
Average Latency:     1.076934583s
Min Latency:         2.668875ms
Max Latency:         1.696373042s
Latency Range:       1.693704167s (635.6x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.16
Signals/Second:      46.43
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 2 batches failed
