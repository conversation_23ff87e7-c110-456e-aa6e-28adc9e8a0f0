Starting performance test [1z1u7v] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1z1u7v] Batch 1/10: 50 signals, 185.005333ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1z1u7v] Batch 2/10: 50 signals, 19.271625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[1z1u7v] Batch 3/10: 50 signals, 7.039667ms, success: false
[1z1u7v] Batch 4/10: 50 signals, 701.520083ms, success: true
[1z1u7v] Batch 5/10: 50 signals, 705.954625ms, success: true
[1z1u7v] Batch 6/10: 50 signals, 1.065082833s, success: true
[1z1u7v] Batch 7/10: 50 signals, 931.183416ms, success: true
[1z1u7v] Batch 8/10: 50 signals, 1.110176292s, success: true
[1z1u7v] Batch 9/10: 50 signals, 1.673097708s, success: true
[1z1u7v] Batch 10/10: 50 signals, 1.602693625s, success: true

============================================================
PERFORMANCE TEST RESULTS [1z1u7v]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 8.001410709s
Total Request Time:  8.001025207s
Average Latency:     1.143003601s
Min Latency:         7.039667ms
Max Latency:         1.673097708s
Latency Range:       1.666058041s (237.7x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.25
Signals/Second:      43.74
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
