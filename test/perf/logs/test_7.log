Starting performance test [sx7svh] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[sx7svh] Batch 1/10: 50 signals, 816.688584ms, success: true
[sx7svh] Batch 2/10: 50 signals, 711.262083ms, success: true
[sx7svh] Batch 3/10: 50 signals, 888.430042ms, success: true
[sx7svh] Batch 4/10: 50 signals, 988.094291ms, success: true
[sx7svh] Batch 5/10: 50 signals, 1.19246125s, success: true
[sx7svh] Batch 6/10: 50 signals, 1.618301959s, success: true
[sx7svh] Batch 7/10: 50 signals, 1.576965166s, success: true
[sx7svh] Batch 8/10: 50 signals, 1.517522708s, success: true
[sx7svh] Batch 9/10: 50 signals, 1.558509709s, success: true
[sx7svh] Batch 10/10: 50 signals, 1.341317625s, success: true

============================================================
PERFORMANCE TEST RESULTS [sx7svh]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.210204417s
Total Request Time:  12.209553417s
Average Latency:     1.220955341s
Min Latency:         711.262083ms
Max Latency:         1.618301959s
Latency Range:       907.039876ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.82
Signals/Second:      40.95
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
