Starting performance test [dhaafr] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 1/10: 50 signals, 189.427542ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 2/10: 50 signals, 14.648541ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 3/10: 50 signals, 15.182167ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 4/10: 50 signals, 37.297125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 5/10: 50 signals, 6.317167ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 6/10: 50 signals, 12.209ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 7/10: 50 signals, 7.118083ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 8/10: 50 signals, 16.602917ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 9/10: 50 signals, 6.759875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[dhaafr] Batch 10/10: 50 signals, 7.17375ms, success: false

============================================================
PERFORMANCE TEST RESULTS [dhaafr]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 312.88325ms
Total Request Time:  312.736167ms
Average Latency:     0s
Min Latency:         6.317167ms
Max Latency:         189.427542ms
Latency Range:       183.110375ms (30.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     31.96
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
