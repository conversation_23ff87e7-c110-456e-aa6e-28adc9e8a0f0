Starting performance test [l64saq] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[l64saq] Batch 1/10: 50 signals, 170.791375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[l64saq] Batch 2/10: 50 signals, 10.248791ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[l64saq] Batch 3/10: 50 signals, 20.496625ms, success: false
[l64saq] Batch 4/10: 50 signals, 757.440542ms, success: true
[l64saq] Batch 5/10: 50 signals, 710.351625ms, success: true
[l64saq] Batch 6/10: 50 signals, 1.03929575s, success: true
[l64saq] Batch 7/10: 50 signals, 877.126208ms, success: true
[l64saq] Batch 8/10: 50 signals, 1.282056458s, success: true
[l64saq] Batch 9/10: 50 signals, 1.631172417s, success: true
[l64saq] Batch 10/10: 50 signals, 1.686987541s, success: true

============================================================
PERFORMANCE TEST RESULTS [l64saq]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 8.18636325s
Total Request Time:  8.185967332s
Average Latency:     1.169423904s
Min Latency:         10.248791ms
Max Latency:         1.686987541s
Latency Range:       1.67673875s (164.6x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.22
Signals/Second:      42.75
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
