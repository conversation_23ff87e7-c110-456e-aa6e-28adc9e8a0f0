Starting performance test [l07158] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[l07158] Batch 1/10: 50 signals, 290.1585ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[l07158] Batch 2/10: 50 signals, 4.8665ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[l07158] Batch 3/10: 50 signals, 11.75525ms, success: false
[l07158] Batch 4/10: 50 signals, 795.829ms, success: true
[l07158] Batch 5/10: 50 signals, 780.115167ms, success: true
[l07158] Batch 6/10: 50 signals, 943.97675ms, success: true
[l07158] Batch 7/10: 50 signals, 1.064938333s, success: true
[l07158] Batch 8/10: 50 signals, 1.405019917s, success: true
[l07158] Batch 9/10: 50 signals, 1.434940375s, success: true
[l07158] Batch 10/10: 50 signals, 1.691331417s, success: true

============================================================
PERFORMANCE TEST RESULTS [l07158]
============================================================
Total Requests:      10
Successful Batches:  8
Failed Batches:      2
Total Signals:       400
============================================================
TIMING METRICS:
Total Test Duration: 8.423261708s
Total Request Time:  8.422931209s
Average Latency:     1.052866401s
Min Latency:         4.8665ms
Max Latency:         1.691331417s
Latency Range:       1.686464917s (347.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.19
Signals/Second:      47.49
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 2 batches failed
