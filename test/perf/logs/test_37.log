Starting performance test [fu2svt] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[fu2svt] Batch 1/10: 50 signals, 182.686375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[fu2svt] Batch 2/10: 50 signals, 8.979417ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[fu2svt] Batch 3/10: 50 signals, 9.155958ms, success: false
[fu2svt] Batch 4/10: 50 signals, 702.145125ms, success: true
[fu2svt] Batch 5/10: 50 signals, 711.810375ms, success: true
[fu2svt] Batch 6/10: 50 signals, 1.068147708s, success: true
[fu2svt] Batch 7/10: 50 signals, 827.913917ms, success: true
[fu2svt] Batch 8/10: 50 signals, 1.220051458s, success: true
[fu2svt] Batch 9/10: 50 signals, 1.676287166s, success: true
[fu2svt] Batch 10/10: 50 signals, 1.609560584s, success: true

============================================================
PERFORMANCE TEST RESULTS [fu2svt]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 8.017263917s
Total Request Time:  8.016738083s
Average Latency:     1.145248297s
Min Latency:         8.979417ms
Max Latency:         1.676287166s
Latency Range:       1.667307749s (186.7x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.25
Signals/Second:      43.66
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
