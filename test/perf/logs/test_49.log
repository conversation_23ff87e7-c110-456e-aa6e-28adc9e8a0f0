Starting performance test [vkrihz] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 1/10: 50 signals, 198.596709ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 2/10: 50 signals, 19.103417ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 3/10: 50 signals, 13.891458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 4/10: 50 signals, 7.081ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 5/10: 50 signals, 3.6815ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 6/10: 50 signals, 9.394625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 7/10: 50 signals, 9.277541ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 8/10: 50 signals, 12.404125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 9/10: 50 signals, 5.112ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vkrihz] Batch 10/10: 50 signals, 8.371541ms, success: false

============================================================
PERFORMANCE TEST RESULTS [vkrihz]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 286.9805ms
Total Request Time:  286.913916ms
Average Latency:     0s
Min Latency:         3.6815ms
Max Latency:         198.596709ms
Latency Range:       194.915209ms (53.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     34.85
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
