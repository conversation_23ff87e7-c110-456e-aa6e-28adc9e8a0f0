Starting performance test [ujqr7j] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[ujqr7j] Batch 1/10: 50 signals, 123.160667ms, success: true
[ujqr7j] Batch 2/10: 50 signals, 214.651709ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 3/10: 50 signals, 5.494375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 4/10: 50 signals, 3.179875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 5/10: 50 signals, 3.427708ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 6/10: 50 signals, 2.401583ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 7/10: 50 signals, 3.894709ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 8/10: 50 signals, 2.196ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 9/10: 50 signals, 2.340834ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[ujqr7j] Batch 10/10: 50 signals, 1.512166ms, success: false

============================================================
PERFORMANCE TEST RESULTS [ujqr7j]
============================================================
Total Requests:      10
Successful Batches:  2
Failed Batches:      8
Total Signals:       100
============================================================
TIMING METRICS:
Total Test Duration: 362.391334ms
Total Request Time:  362.259626ms
Average Latency:     181.129813ms
Min Latency:         1.512166ms
Max Latency:         214.651709ms
Latency Range:       213.139543ms (141.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     27.59
Signals/Second:      275.94
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 8 batches failed
