Starting performance test [yky3f0] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[yky3f0] Batch 1/10: 50 signals, 286.957542ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[yky3f0] Batch 2/10: 50 signals, 7.26575ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[yky3f0] Batch 3/10: 50 signals, 9.011209ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[yky3f0] Batch 4/10: 50 signals, 9.840375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[yky3f0] Batch 5/10: 50 signals, 6.018458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[yky3f0] Batch 6/10: 50 signals, 2.148875ms, success: false
[yky3f0] Batch 7/10: 50 signals, 806.008625ms, success: true
[yky3f0] Batch 8/10: 50 signals, 767.657167ms, success: true
[yky3f0] Batch 9/10: 50 signals, 1.087382542s, success: true
[yky3f0] Batch 10/10: 50 signals, 914.613833ms, success: true

============================================================
PERFORMANCE TEST RESULTS [yky3f0]
============================================================
Total Requests:      10
Successful Batches:  5
Failed Batches:      5
Total Signals:       250
============================================================
TIMING METRICS:
Total Test Duration: 3.897203083s
Total Request Time:  3.896904376s
Average Latency:     779.380875ms
Min Latency:         2.148875ms
Max Latency:         1.087382542s
Latency Range:       1.085233667s (506.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     2.57
Signals/Second:      64.15
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 5 batches failed
