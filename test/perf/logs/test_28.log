Starting performance test [05npls] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[05npls] Batch 1/10: 50 signals, 494.263208ms, success: true
[05npls] Batch 2/10: 50 signals, 814.019959ms, success: true
[05npls] Batch 3/10: 50 signals, 786.687458ms, success: true
[05npls] Batch 4/10: 50 signals, 1.091041334s, success: true
[05npls] Batch 5/10: 50 signals, 946.377042ms, success: true
[05npls] Batch 6/10: 50 signals, 1.478595375s, success: true
[05npls] Batch 7/10: 50 signals, 1.583147833s, success: true
[05npls] Batch 8/10: 50 signals, 1.49157675s, success: true
[05npls] Batch 9/10: 50 signals, 1.345694458s, success: true
[05npls] Batch 10/10: 50 signals, 1.369782416s, success: true

============================================================
PERFORMANCE TEST RESULTS [05npls]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.401659542s
Total Request Time:  11.401185833s
Average Latency:     1.140118583s
Min Latency:         494.263208ms
Max Latency:         1.583147833s
Latency Range:       1.088884625s (3.2x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.88
Signals/Second:      43.85
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
