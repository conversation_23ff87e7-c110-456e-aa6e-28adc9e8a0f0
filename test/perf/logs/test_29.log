Starting performance test [rvjibp] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[rvjibp] Batch 1/10: 50 signals, 186.830833ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[rvjibp] Batch 2/10: 50 signals, 15.595458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[rvjibp] Batch 3/10: 50 signals, 20.492958ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[rvjibp] Batch 4/10: 50 signals, 17.067542ms, success: false
[rvjibp] Batch 5/10: 50 signals, 763.830291ms, success: true
[rvjibp] Batch 6/10: 50 signals, 706.454875ms, success: true
[rvjibp] Batch 7/10: 50 signals, 1.039635625s, success: true
[rvjibp] Batch 8/10: 50 signals, 976.798417ms, success: true
[rvjibp] Batch 9/10: 50 signals, 1.195383042s, success: true
[rvjibp] Batch 10/10: 50 signals, 1.614470208s, success: true

============================================================
PERFORMANCE TEST RESULTS [rvjibp]
============================================================
Total Requests:      10
Successful Batches:  6
Failed Batches:      4
Total Signals:       300
============================================================
TIMING METRICS:
Total Test Duration: 6.538695917s
Total Request Time:  6.536559249s
Average Latency:     1.089426541s
Min Latency:         15.595458ms
Max Latency:         1.614470208s
Latency Range:       1.59887475s (103.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.53
Signals/Second:      45.88
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 4 batches failed
