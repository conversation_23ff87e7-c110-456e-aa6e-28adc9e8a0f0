Starting performance test [vnwzhm] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[vnwzhm] Batch 1/10: 50 signals, 198.366208ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 2/10: 50 signals, 17.167583ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 3/10: 50 signals, 28.765875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 4/10: 50 signals, 13.756291ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 5/10: 50 signals, 6.035791ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 6/10: 50 signals, 13.046ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[vnwzhm] Batch 7/10: 50 signals, 5.963167ms, success: false
[vnwzhm] Batch 8/10: 50 signals, 803.425541ms, success: true
[vnwzhm] Batch 9/10: 50 signals, 713.175417ms, success: true
[vnwzhm] Batch 10/10: 50 signals, 1.034131917s, success: true

============================================================
PERFORMANCE TEST RESULTS [vnwzhm]
============================================================
Total Requests:      10
Successful Batches:  4
Failed Batches:      6
Total Signals:       200
============================================================
TIMING METRICS:
Total Test Duration: 2.834033458s
Total Request Time:  2.83383379s
Average Latency:     708.458447ms
Min Latency:         5.963167ms
Max Latency:         1.034131917s
Latency Range:       1.02816875s (173.4x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     3.53
Signals/Second:      70.57
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 6 batches failed
