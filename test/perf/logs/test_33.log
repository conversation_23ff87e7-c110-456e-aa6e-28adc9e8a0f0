Starting performance test [anvaj1] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[anvaj1] Batch 1/10: 50 signals, 442.0955ms, success: true
[anvaj1] Batch 2/10: 50 signals, 862.02625ms, success: true
[anvaj1] Batch 3/10: 50 signals, 789.917209ms, success: true
[anvaj1] Batch 4/10: 50 signals, 1.096029958s, success: true
[anvaj1] Batch 5/10: 50 signals, 1.116181208s, success: true
[anvaj1] Batch 6/10: 50 signals, 1.48973475s, success: true
[anvaj1] Batch 7/10: 50 signals, 1.538077458s, success: true
[anvaj1] Batch 8/10: 50 signals, 1.503496042s, success: true
[anvaj1] Batch 9/10: 50 signals, 1.452974917s, success: true
[anvaj1] Batch 10/10: 50 signals, 1.34398325s, success: true

============================================================
PERFORMANCE TEST RESULTS [anvaj1]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.635045125s
Total Request Time:  11.634516542s
Average Latency:     1.163451654s
Min Latency:         442.0955ms
Max Latency:         1.538077458s
Latency Range:       1.095981958s (3.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.86
Signals/Second:      42.97
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
