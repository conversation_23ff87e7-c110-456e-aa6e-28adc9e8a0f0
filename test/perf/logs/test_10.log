Starting performance test [bq31jz] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[bq31jz] Batch 1/10: 50 signals, 291.60075ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 2/10: 50 signals, 6.783959ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 3/10: 50 signals, 9.761125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 4/10: 50 signals, 10.714416ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 5/10: 50 signals, 2.305917ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 6/10: 50 signals, 2.94775ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 7/10: 50 signals, 1.920375ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 8/10: 50 signals, 3.952208ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bq31jz] Batch 9/10: 50 signals, 2.69075ms, success: false
[bq31jz] Batch 10/10: 50 signals, 800.556416ms, success: true

============================================================
PERFORMANCE TEST RESULTS [bq31jz]
============================================================
Total Requests:      10
Successful Batches:  2
Failed Batches:      8
Total Signals:       100
============================================================
TIMING METRICS:
Total Test Duration: 1.133403s
Total Request Time:  1.133233666s
Average Latency:     566.616833ms
Min Latency:         1.920375ms
Max Latency:         800.556416ms
Latency Range:       798.636041ms (416.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     8.82
Signals/Second:      88.23
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 8 batches failed
