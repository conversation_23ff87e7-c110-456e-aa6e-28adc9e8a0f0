Starting performance test [y86tj0] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[y86tj0] Batch 1/10: 50 signals, 128.382292ms, success: true
[y86tj0] Batch 2/10: 50 signals, 278.618834ms, success: true
[y86tj0] Batch 3/10: 50 signals, 805.713458ms, success: true
[y86tj0] Batch 4/10: 50 signals, 783.483166ms, success: true
[y86tj0] Batch 5/10: 50 signals, 1.097383209s, success: true
[y86tj0] Batch 6/10: 50 signals, 924.1105ms, success: true
[y86tj0] Batch 7/10: 50 signals, 1.46958325s, success: true
[y86tj0] Batch 8/10: 50 signals, 1.439151834s, success: true
[y86tj0] Batch 9/10: 50 signals, 1.510803083s, success: true
[y86tj0] Batch 10/10: 50 signals, 1.362145958s, success: true

============================================================
PERFORMANCE TEST RESULTS [y86tj0]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 9.799836166s
Total Request Time:  9.799375584s
Average Latency:     979.937558ms
Min Latency:         128.382292ms
Max Latency:         1.510803083s
Latency Range:       1.382420791s (11.8x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.02
Signals/Second:      51.02
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
