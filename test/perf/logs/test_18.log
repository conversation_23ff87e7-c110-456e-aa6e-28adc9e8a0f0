Starting performance test [b20w6y] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[b20w6y] Batch 1/10: 50 signals, 211.035916ms, success: false
[b20w6y] Batch 2/10: 50 signals, 705.027125ms, success: true
[b20w6y] Batch 3/10: 50 signals, 783.639833ms, success: true
[b20w6y] Batch 4/10: 50 signals, 1.047389042s, success: true
[b20w6y] Batch 5/10: 50 signals, 976.209291ms, success: true
[b20w6y] Batch 6/10: 50 signals, 1.18512175s, success: true
[b20w6y] Batch 7/10: 50 signals, 1.632945042s, success: true
[b20w6y] Batch 8/10: 50 signals, 1.681623583s, success: true
[b20w6y] Batch 9/10: 50 signals, 1.576195125s, success: true
[b20w6y] Batch 10/10: 50 signals, 1.310801084s, success: true

============================================================
PERFORMANCE TEST RESULTS [b20w6y]
============================================================
Total Requests:      10
Successful Batches:  9
Failed Batches:      1
Total Signals:       450
============================================================
TIMING METRICS:
Total Test Duration: 11.110516375s
Total Request Time:  11.109987791s
Average Latency:     1.234443087s
Min Latency:         211.035916ms
Max Latency:         1.681623583s
Latency Range:       1.470587667s (8.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.90
Signals/Second:      40.50
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 1 batches failed
