Starting performance test [gjekj3] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[gjekj3] Batch 1/10: 50 signals, 789.002167ms, success: true
[gjekj3] Batch 2/10: 50 signals, 647.606625ms, success: true
[gjekj3] Batch 3/10: 50 signals, 884.031041ms, success: true
[gjekj3] Batch 4/10: 50 signals, 1.078279791s, success: true
[gjekj3] Batch 5/10: 50 signals, 1.03601125s, success: true
[gjekj3] Batch 6/10: 50 signals, 1.651858208s, success: true
[gjekj3] Batch 7/10: 50 signals, 1.510864667s, success: true
[gjekj3] Batch 8/10: 50 signals, 1.704054708s, success: true
[gjekj3] Batch 9/10: 50 signals, 1.218170208s, success: true
[gjekj3] Batch 10/10: 50 signals, 1.394942833s, success: true

============================================================
PERFORMANCE TEST RESULTS [gjekj3]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.916429458s
Total Request Time:  11.914821498s
Average Latency:     1.191482149s
Min Latency:         647.606625ms
Max Latency:         1.704054708s
Latency Range:       1.056448083s (2.6x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.84
Signals/Second:      41.96
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
