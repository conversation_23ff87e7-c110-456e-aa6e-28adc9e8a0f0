Starting performance test [pch41t] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[pch41t] Batch 1/10: 50 signals, 214.046917ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[pch41t] Batch 2/10: 50 signals, 19.081583ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[pch41t] Batch 3/10: 50 signals, 12.1505ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[pch41t] Batch 4/10: 50 signals, 13.220333ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[pch41t] Batch 5/10: 50 signals, 3.443209ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[pch41t] Batch 6/10: 50 signals, 7.870125ms, success: false
[pch41t] Batch 7/10: 50 signals, 753.975333ms, success: true
[pch41t] Batch 8/10: 50 signals, 768.405916ms, success: true
[pch41t] Batch 9/10: 50 signals, 958.617875ms, success: true
[pch41t] Batch 10/10: 50 signals, 974.213708ms, success: true

============================================================
PERFORMANCE TEST RESULTS [pch41t]
============================================================
Total Requests:      10
Successful Batches:  5
Failed Batches:      5
Total Signals:       250
============================================================
TIMING METRICS:
Total Test Duration: 3.725862292s
Total Request Time:  3.725025499s
Average Latency:     745.005099ms
Min Latency:         3.443209ms
Max Latency:         974.213708ms
Latency Range:       970.770499ms (282.9x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     2.68
Signals/Second:      67.10
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 5 batches failed
