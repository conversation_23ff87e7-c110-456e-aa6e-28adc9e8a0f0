Starting performance test [z9sl6p] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[z9sl6p] Batch 1/10: 50 signals, 494.932875ms, success: true
[z9sl6p] Batch 2/10: 50 signals, 834.541334ms, success: true
[z9sl6p] Batch 3/10: 50 signals, 766.653292ms, success: true
[z9sl6p] Batch 4/10: 50 signals, 1.095076208s, success: true
[z9sl6p] Batch 5/10: 50 signals, 938.099292ms, success: true
[z9sl6p] Batch 6/10: 50 signals, 1.481201041s, success: true
[z9sl6p] Batch 7/10: 50 signals, 1.587523958s, success: true
[z9sl6p] Batch 8/10: 50 signals, 1.634579792s, success: true
[z9sl6p] Batch 9/10: 50 signals, 1.1905225s, success: true
[z9sl6p] Batch 10/10: 50 signals, 1.385828791s, success: true

============================================================
PERFORMANCE TEST RESULTS [z9sl6p]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.409493792s
Total Request Time:  11.408959083s
Average Latency:     1.140895908s
Min Latency:         494.932875ms
Max Latency:         1.634579792s
Latency Range:       1.139646917s (3.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.88
Signals/Second:      43.82
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
