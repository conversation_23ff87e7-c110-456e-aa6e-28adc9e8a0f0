package utils

import (
	"crypto/rand"
	"encoding/base32"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"golang.org/x/text/runes"
	"golang.org/x/text/transform"
	"golang.org/x/text/unicode/norm"
)

// GenerateSlug generates a URL-friendly slug from a given string.
// slugs identify a set of versioned signal_types describing the same data set.
// Only the owner of the inital slug can update it or add new versions.
// A slug can't be owned by more than one user.
func GenerateSlug(input string) (string, error) {
	if input == "" {
		return "", fmt.Errorf("no input string supplied to GenerateSlug")
	}

	normalized := norm.NFD.String(input)

	withoutDiacritics, _, err := transform.String(runes.Remove(runes.In(unicode.Mn)), normalized)
	if err != nil {
		return "", fmt.Errorf("error creating slug: %v", err)
	}

	lowerCase := strings.ToLower(withoutDiacritics)

	reg := regexp.MustCompile(`[^a-z0-9\- ]+`) // Include space in the character set to handle it separately
	hyphenated := reg.ReplaceAllString(lowerCase, "-")

	spaceReg := regexp.MustCompile(`[ ]+`)
	hyphenated = spaceReg.ReplaceAllString(hyphenated, "-")

	trimmed := strings.Trim(hyphenated, "-")

	return trimmed, nil
}

// ValidateGithubFileURL validates GitHub URLs for schema and readme files with enhanced SSRF protection
// Supports special URLs like skip validation for schemas
func ValidateGithubFileURL(rawURL string, fileType string) error {
	// Handle special skip validation URL for schemas
	if fileType == "schema" && rawURL == "https://github.com/skip/validation/main/schema.json" {
		return nil
	}

	parsedURL, err := url.ParseRequestURI(rawURL)
	if err != nil {
		return fmt.Errorf("invalid URL supplied: %w", err)
	}

	// Strict validation: only allow HTTPS and github.com
	if parsedURL.Scheme != "https" {
		return fmt.Errorf("only HTTPS URLs are allowed")
	}

	if parsedURL.Host != "github.com" {
		return fmt.Errorf("only GitHub URLs are allowed (github.com)")
	}

	// Validate path structure and prevent directory traversal
	if strings.Contains(parsedURL.Path, "..") || strings.Contains(parsedURL.Path, "//") {
		return fmt.Errorf("invalid path: directory traversal detected")
	}

	// Ensure path follows GitHub repository structure: /owner/repo/...
	pathParts := strings.Split(strings.Trim(parsedURL.Path, "/"), "/")
	if len(pathParts) < 3 {
		return fmt.Errorf("invalid GitHub URL structure: must be https://github.com/owner/repo/...")
	}

	// Validate owner and repo names (GitHub naming rules)
	ownerPattern := `^[a-zA-Z0-9]([a-zA-Z0-9\-])*[a-zA-Z0-9]$|^[a-zA-Z0-9]$`
	repoPattern := `^[a-zA-Z0-9._-]+$`

	if matched, _ := regexp.MatchString(ownerPattern, pathParts[0]); !matched {
		return fmt.Errorf("invalid GitHub owner name")
	}
	if matched, _ := regexp.MatchString(repoPattern, pathParts[1]); !matched {
		return fmt.Errorf("invalid GitHub repository name")
	}

	// Validate file extension
	switch fileType {
	case "schema":
		if !strings.HasSuffix(parsedURL.Path, ".json") {
			return fmt.Errorf("schema URL must end with .json")
		}
	case "readme":
		if !strings.HasSuffix(parsedURL.Path, ".md") {
			return fmt.Errorf("readme URL must end with .md")
		}
	default:
		return fmt.Errorf("internal server error - invalid file type: %s", fileType)
	}

	return nil
}

// FetchGithubFileContent securely fetches content from GitHub URLs with SSRF protection
// Handles both blob URLs (from GitHub web interface) and raw URLs
// Disables redirects to prevent SSRF attacks
func FetchGithubFileContent(rawURL string) (string, error) {
	originalURL := rawURL

	// Handle special skip validation URL
	if rawURL == "https://github.com/skip/validation/main/schema.json" {
		return "{}", nil
	}

	// Convert GitHub blob URLs to raw URLs for direct file access
	// Example: https://github.com/org/repo/blob/2025.01.01/file.json
	//       -> https://raw.githubusercontent.com/org/repo/2025.01.01/file.json
	if strings.HasPrefix(rawURL, "https://github.com/") {
		rawURL = strings.Replace(rawURL, "https://github.com/", "https://raw.githubusercontent.com/", 1)
		rawURL = strings.Replace(rawURL, "/blob/", "/", 1)
	}

	// Create HTTP client with SSRF protection
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Disable redirects to prevent SSRF attacks
			return http.ErrUseLastResponse
		},
	}

	// #nosec G107 -- URL is validated to be GitHub-only before this function is called
	req, err := http.NewRequest("GET", rawURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request for %s: %w", originalURL, err)
	}

	// Set User-Agent to identify our service
	req.Header.Set("User-Agent", "signalsd/1.0")

	res, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to fetch content from %s: %w", originalURL, err)
	}
	defer res.Body.Close()

	// Handle redirects explicitly (since we disabled automatic redirects)
	if res.StatusCode >= 300 && res.StatusCode < 400 {
		return "", fmt.Errorf("redirects are not allowed for security reasons (status: %d)", res.StatusCode)
	}

	if res.StatusCode != http.StatusOK {
		if res.StatusCode == http.StatusNotFound {
			return "", fmt.Errorf("file not found at %s", originalURL)
		}
		return "", fmt.Errorf("fetch failed with status: %d", res.StatusCode)
	}

	bodyBytes, err := io.ReadAll(res.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	return string(bodyBytes), nil
}

// CheckSignalTypeURL is deprecated, use ValidateGithubFileURL instead
// Kept for backward compatibility
func CheckSignalTypeURL(rawURL string, urlType string) error {
	return ValidateGithubFileURL(rawURL, urlType)
}

// expects a semver in the form "major.minor.patch" and increments major/minor/patch according to supplied bump_type
func IncrementSemVer(bump_type string, semVer string) (string, error) {

	components := strings.Split(semVer, ".")

	if len(components) != 3 {
		return "", fmt.Errorf("can't bump version, invalid semVer supplied")
	}

	major, err := strconv.Atoi(components[0])
	if err != nil {
		return "", fmt.Errorf("can't bump version, invalid semVer supplied")
	}
	minor, err := strconv.Atoi(components[1])
	if err != nil {
		return "", fmt.Errorf("can't bump version, invalid semVer supplied")
	}
	patch, err := strconv.Atoi(components[2])
	if err != nil {
		return "", fmt.Errorf("can't bump version, invalid semVer supplied")
	}

	switch bump_type {
	case "major":
		major++
		minor = 0
		patch = 0
	case "minor":
		minor++
		patch = 0
	case "patch":
		patch++
	default:
		return "", fmt.Errorf("can't bump version, invalid bump type supplied")
	}
	return fmt.Sprintf("%d.%d.%d", major, minor, patch), nil
}

func GetScheme(r *http.Request) string {
	if r.TLS != nil {
		return "https"
	}

	// Check common reverse proxy headers
	if scheme := r.Header.Get("X-Forwarded-Proto"); scheme != "" {
		return scheme
	}
	return "http"
}

// check for valid origins, e.g http://localhost:8080 , https://example.com etc
func IsValidOrigin(urlStr string) bool {
	re := regexp.MustCompile(`^(https?):\/\/([a-zA-Z0-9_\-\.]+)(:\d+)?$`)
	return re.MatchString(urlStr)
}

// check that the supplied string conforms to the date formats supported by the API (ISO 8601 or YYYY-MM-DD)
// Assume YYYY-MM-DD is the start of day in UTC e.g 2006-01-02T00:00:00Z
func ParseDateTime(dateString string) (time.Time, error) {
	isoLayouts := []string{
		time.RFC3339,     // e.g 2006-01-02T15:04:05Z07:00
		time.RFC3339Nano, // e.g 2006-01-02T15:04:05.999999999Z07:00
	}

	for _, layout := range isoLayouts {
		if t, err := time.Parse(layout, dateString); err == nil {
			return t, nil
		}
	}

	//If not an ISO 8601 with timezone, try YYYY.MM.DD
	if t, err := time.Parse("2006-01-02", dateString); err == nil {
		return t.UTC(), nil
	}

	return time.Time{}, fmt.Errorf("unsupported date format: %s. Expected ISO 8601 with timezone (e.g., 2006-01-02T15:04:05+07:00) or YYYY-MM-DD (e.g., 2006-01-02)", dateString)
}

func GenerateClientID(organization string) (string, error) {
	organizationSlug, err := GenerateSlug(organization)
	if err != nil {
		return "", err
	}

	randomBytes := make([]byte, 6)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", err
	}
	randomSuffix := base32.StdEncoding.EncodeToString(randomBytes)[:8]

	return fmt.Sprintf("sa_%s_%s", strings.ToLower(organizationSlug), strings.ToLower(randomSuffix)), nil
}
